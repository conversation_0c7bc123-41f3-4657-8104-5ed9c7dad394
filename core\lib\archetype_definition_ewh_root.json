{"ewh_root": {"policy_assignments": ["Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Deny-VM-Internet-SSH-RDP", "Deny-Resource-Locations", "Deny-RSG-Locations", "Require-Mandatory-Tags", "Enforce-Naming", "Enforce-Naming-Databases", "Enforce-Name-Containers"], "policy_definitions": ["naming_convention", "naming_convention_databases", "naming_convention_containers", "secure_storage_accounts", "enforce_network_security_groups", "require_mandatory_tags_with_rules"], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Priority", "data_zone": "Public", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {"Owner": ["module.azure_ad_groups.azuread_group.groups[\"infras_team_breakglass\"].object_id"], "Contributor": ["module.azure_ad_groups.azuread_group.groups[\"infras_team_admin\"].object_id", "module.azure_ad_groups.azuread_group.groups[\"app_teams_admin\"].object_id"], "Reader": ["module.azure_ad_groups.azuread_group.groups[\"infras_team_operation\"].object_id", "module.azure_ad_groups.azuread_group.groups[\"app_teams_operation\"].object_id"], "User Access Administrator": ["module.azure_ad_groups.azuread_group.groups[\"app_teams_super_admin\"].object_id"], "Billing Reader": ["module.azure_ad_groups.azuread_group.groups[\"finance_team\"].object_id"]}}}}