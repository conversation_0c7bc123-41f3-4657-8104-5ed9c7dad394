# Azure Bastion Configuration
# This file contains Azure Bastion host configuration for secure VM access

# Note: Public IP for Azure Bastion is now managed by ALZ module
/*
resource "azurerm_public_ip" "bastion_public_ip" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-pip-bastion-${var.primary_location}"
  location            = var.primary_location
  resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"
  allocation_method   = "Static"
  sku                 = "Standard"
  zones               = ["1", "2", "3"]

  tags = var.connectivity_resources_tags
}
*/

# Note: Azure Bastion is now managed by ALZ module
# The ALZ module will create the Bastion host with the configuration specified in settings.connectivity.tf
# This file is kept for reference and future customizations if needed

# If you need to customize <PERSON><PERSON><PERSON> beyond ALZ module capabilities, uncomment and modify below:
/*
resource "azurerm_bastion_host" "hub_bastion" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-bastion-${var.primary_location}"
  location            = var.primary_location
  resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"
  sku                 = "Standard"
  scale_units         = 2

  # Enable advanced features
  copy_paste_enabled     = true
  file_copy_enabled      = true
  ip_connect_enabled     = true
  shareable_link_enabled = false
  tunneling_enabled      = true

  ip_configuration {
    name                 = "bastion-ip-config"
    subnet_id            = data.azurerm_subnet.bastion_subnet.id
    public_ip_address_id = azurerm_public_ip.bastion_public_ip.id
  }

  tags = var.connectivity_resources_tags
}
*/

# Note: Data sources are commented out as ALZ module manages Bastion resources
/*
data "azurerm_subnet" "bastion_subnet" {
  depends_on = [module.alz_connectivity]

  name                 = "AzureBastionSubnet"
  virtual_network_name = "${var.root_id}-hub-${var.primary_location}"
  resource_group_name  = "${var.root_id}-connectivity-${var.primary_location}"
}
*/

# Note: NSG for Bastion is managed by ALZ module
/*
resource "azurerm_network_security_group" "bastion_nsg" {
  depends_on = [module.alz_connectivity]

  name                = "${var.root_id}-nsg-bastion-${var.primary_location}"
  location            = var.primary_location
  resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"

  # Inbound rules for Azure Bastion
  security_rule {
    name                       = "AllowHttpsInbound"
    priority                   = 1000
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "Internet"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "AllowGatewayManagerInbound"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "GatewayManager"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "AllowAzureLoadBalancerInbound"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "AzureLoadBalancer"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "AllowBastionHostCommunication"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_ranges    = ["8080", "5701"]
    source_address_prefix      = "VirtualNetwork"
    destination_address_prefix = "VirtualNetwork"
  }

  # Outbound rules for Azure Bastion
  security_rule {
    name                       = "AllowSshRdpOutbound"
    priority                   = 1000
    direction                  = "Outbound"
    access                     = "Allow"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_ranges    = ["22", "3389"]
    source_address_prefix      = "*"
    destination_address_prefix = "VirtualNetwork"
  }

  security_rule {
    name                       = "AllowAzureCloudOutbound"
    priority                   = 1001
    direction                  = "Outbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "AzureCloud"
  }

  security_rule {
    name                       = "AllowBastionCommunication"
    priority                   = 1002
    direction                  = "Outbound"
    access                     = "Allow"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_ranges    = ["8080", "5701"]
    source_address_prefix      = "VirtualNetwork"
    destination_address_prefix = "VirtualNetwork"
  }

  security_rule {
    name                       = "AllowGetSessionInformation"
    priority                   = 1003
    direction                  = "Outbound"
    access                     = "Allow"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "*"
    destination_address_prefix = "Internet"
  }

  tags = var.connectivity_resources_tags
}

# Associate NSG with Bastion Subnet
resource "azurerm_subnet_network_security_group_association" "bastion_nsg_association" {
  depends_on = [azurerm_bastion_host.hub_bastion]

  subnet_id                 = data.azurerm_subnet.bastion_subnet.id
  network_security_group_id = azurerm_network_security_group.bastion_nsg.id
}
*/
