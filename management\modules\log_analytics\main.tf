# Log Analytics Module
# Provides Log Analytics workspace and solutions

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# Log Analytics Workspace
resource "azurerm_log_analytics_workspace" "main" {
  name                = "${var.resource_prefix}-law-${var.location}"
  location            = var.location
  resource_group_name = var.resource_group_name
  sku                 = var.sku
  retention_in_days   = var.retention_in_days
  daily_quota_gb      = var.daily_quota_gb

  tags = var.tags
}

# Log Analytics Solutions
resource "azurerm_log_analytics_solution" "vm_insights" {
  count                 = var.enable_monitoring_for_vm ? 1 : 0
  solution_name         = "VMInsights"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/VMInsights"
  }

  tags = var.tags
}

resource "azurerm_log_analytics_solution" "change_tracking" {
  count                 = var.enable_change_tracking ? 1 : 0
  solution_name         = "ChangeTracking"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/ChangeTracking"
  }

  tags = var.tags
}

resource "azurerm_log_analytics_solution" "updates" {
  count                 = var.enable_updates ? 1 : 0
  solution_name         = "Updates"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/Updates"
  }

  tags = var.tags
}

resource "azurerm_log_analytics_solution" "security" {
  count                 = var.enable_security ? 1 : 0
  solution_name         = "Security"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/Security"
  }

  tags = var.tags
}

resource "azurerm_log_analytics_solution" "agent_health_assessment" {
  count                 = var.enable_agent_health_assessment ? 1 : 0
  solution_name         = "AgentHealthAssessment"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/AgentHealthAssessment"
  }

  tags = var.tags
}

resource "azurerm_log_analytics_solution" "anti_malware" {
  count                 = var.enable_anti_malware ? 1 : 0
  solution_name         = "AntiMalware"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/AntiMalware"
  }

  tags = var.tags
}

resource "azurerm_log_analytics_solution" "container_insights" {
  count                 = var.enable_container_insights ? 1 : 0
  solution_name         = "ContainerInsights"
  location              = var.location
  resource_group_name   = var.resource_group_name
  workspace_resource_id = azurerm_log_analytics_workspace.main.id
  workspace_name        = azurerm_log_analytics_workspace.main.name

  plan {
    publisher = "Microsoft"
    product   = "OMSGallery/ContainerInsights"
  }

  tags = var.tags
}

# Microsoft Sentinel
resource "azurerm_sentinel_log_analytics_workspace_onboarding" "main" {
  count                = var.enable_sentinel ? 1 : 0
  workspace_id         = azurerm_log_analytics_workspace.main.id
  customer_managed_key_enabled = false
}

# Data Sources
resource "azurerm_log_analytics_datasource_windows_event" "system" {
  count               = var.enable_monitoring_for_vm ? 1 : 0
  name                = "system-events"
  resource_group_name = var.resource_group_name
  workspace_name      = azurerm_log_analytics_workspace.main.name
  event_log_name      = "System"
  event_types         = ["Error", "Warning", "Information"]
}

resource "azurerm_log_analytics_datasource_windows_event" "application" {
  count               = var.enable_monitoring_for_vm ? 1 : 0
  name                = "application-events"
  resource_group_name = var.resource_group_name
  workspace_name      = azurerm_log_analytics_workspace.main.name
  event_log_name      = "Application"
  event_types         = ["Error", "Warning", "Information"]
}

resource "azurerm_log_analytics_datasource_windows_performance_counter" "cpu" {
  count               = var.enable_monitoring_for_vm ? 1 : 0
  name                = "cpu-percentage"
  resource_group_name = var.resource_group_name
  workspace_name      = azurerm_log_analytics_workspace.main.name
  object_name         = "Processor"
  instance_name       = "*"
  counter_name        = "% Processor Time"
  interval_seconds    = 60
}

resource "azurerm_log_analytics_datasource_windows_performance_counter" "memory" {
  count               = var.enable_monitoring_for_vm ? 1 : 0
  name                = "memory-available"
  resource_group_name = var.resource_group_name
  workspace_name      = azurerm_log_analytics_workspace.main.name
  object_name         = "Memory"
  instance_name       = "*"
  counter_name        = "Available MBytes"
  interval_seconds    = 60
}

resource "azurerm_log_analytics_datasource_windows_performance_counter" "disk" {
  count               = var.enable_monitoring_for_vm ? 1 : 0
  name                = "disk-free-space"
  resource_group_name = var.resource_group_name
  workspace_name      = azurerm_log_analytics_workspace.main.name
  object_name         = "LogicalDisk"
  instance_name       = "*"
  counter_name        = "% Free Space"
  interval_seconds    = 60
}
