# Azure Firewall Policy and Rules Configuration - COMMENTED OUT
# This file contains firewall rules for hub-and-spoke network architecture
# Currently commented out as Azure Firewall is not needed

# # Data source to get Azure Firewall information from ALZ module
# data "azurerm_firewall" "hub_firewall" {
#   depends_on = [module.alz_connectivity]
#
#   name                = "${var.root_id}-fw-${var.primary_location}"
#   resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"
# }

# # Use the ALZ-managed Azure Firewall Policy instead of creating a new one
# data "azurerm_firewall_policy" "alz_firewall_policy" {
#   depends_on = [module.alz_connectivity]

#   name                = "${var.root_id}-fw-${var.primary_location}-policy"
#   resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"
# }

# # Network Rule Collection Group - Internal Traffic
# resource "azurerm_firewall_policy_rule_collection_group" "network_rules" {
#   name               = "NetworkRuleCollectionGroup"
#   firewall_policy_id = data.azurerm_firewall_policy.alz_firewall_policy.id
#   priority           = 500

#   # Allow traffic between production networks
#   network_rule_collection {
#     name     = "ProductionNetworkRules"
#     priority = 400
#     action   = "Allow"

#     rule {
#       name                  = "prod-to-prod"
#       protocols             = ["TCP", "UDP"]
#       source_addresses      = ["**********/16"]
#       destination_addresses = ["**********/16"]
#       destination_ports     = ["*"]
#     }

#     rule {
#       name                  = "prod-to-management"
#       protocols             = ["TCP", "UDP"]
#       source_addresses      = ["**********/16"]
#       destination_addresses = ["***********/21"]
#       destination_ports     = ["443", "22", "3389"]
#     }
#   }

#   # Allow traffic between non-production networks
#   network_rule_collection {
#     name     = "NonProductionNetworkRules"
#     priority = 300
#     action   = "Allow"

#     rule {
#       name                  = "nonprod-to-nonprod"
#       protocols             = ["TCP", "UDP"]
#       source_addresses      = ["**********/16"]
#       destination_addresses = ["**********/16"]
#       destination_ports     = ["*"]
#     }

#     rule {
#       name                  = "nonprod-to-management"
#       protocols             = ["TCP", "UDP"]
#       source_addresses      = ["**********/16"]
#       destination_addresses = ["***********/21"]
#       destination_ports     = ["443", "22", "3389"]
#     }
#   }

#   # Management network rules
#   network_rule_collection {
#     name     = "ManagementNetworkRules"
#     priority = 200
#     action   = "Allow"

#     rule {
#       name                  = "management-to-all-spokes"
#       protocols             = ["TCP", "UDP"]
#       source_addresses      = ["***********/21"]
#       destination_addresses = ["**********/16", "**********/16"]
#       destination_ports     = ["443", "22", "3389", "80"]
#     }
#   }

#   # DNS and NTP rules
#   network_rule_collection {
#     name     = "InfrastructureRules"
#     priority = 100
#     action   = "Allow"

#     rule {
#       name                  = "dns-outbound"
#       protocols             = ["UDP"]
#       source_addresses      = ["**********/16", "**********/16", "**********/16"]
#       destination_addresses = ["*************", "*******", "*******"]
#       destination_ports     = ["53"]
#     }

#     rule {
#       name                  = "ntp-outbound"
#       protocols             = ["UDP"]
#       source_addresses      = ["**********/16", "**********/16", "**********/16"]
#       destination_addresses = ["*"]
#       destination_ports     = ["123"]
#     }
#   }
# }

# # Application Rule Collection Group - Internet Access
# resource "azurerm_firewall_policy_rule_collection_group" "application_rules" {
#   name               = "ApplicationRuleCollectionGroup"
#   firewall_policy_id = data.azurerm_firewall_policy.alz_firewall_policy.id
#   priority           = 300

#   # Allow essential internet access
#   application_rule_collection {
#     name     = "AllowInternetAccess"
#     priority = 500
#     action   = "Allow"

#     rule {
#       name = "allow-windows-updates"
#       protocols {
#         type = "Http"
#         port = 80
#       }
#       protocols {
#         type = "Https"
#         port = 443
#       }
#       source_addresses  = ["**********/16", "**********/16", "**********/16"]
#       destination_fqdns = [
#         "*.windowsupdate.microsoft.com",
#         "*.update.microsoft.com",
#         "*.windowsupdate.com",
#         "download.microsoft.com",
#         "*.download.windowsupdate.com"
#       ]
#     }

#     rule {
#       name = "allow-azure-services"
#       protocols {
#         type = "Https"
#         port = 443
#       }
#       source_addresses  = ["**********/16", "**********/16", "**********/16"]
#       destination_fqdns = [
#         "*.azure.com",
#         "*.microsoft.com",
#         "*.microsoftonline.com",
#         "*.azure.net",
#         "management.azure.com",
#         "login.microsoftonline.com"
#       ]
#     }

#     rule {
#       name = "allow-package-managers"
#       protocols {
#         type = "Http"
#         port = 80
#       }
#       protocols {
#         type = "Https"
#         port = 443
#       }
#       source_addresses  = ["**********/16", "**********/16", "**********/16"]
#       destination_fqdns = [
#         "*.ubuntu.com",
#         "*.debian.org",
#         "*.centos.org",
#         "*.redhat.com",
#         "registry.npmjs.org",
#         "*.nuget.org",
#         "pypi.org",
#         "*.pypi.org"
#       ]
#     }
#   }
# }

# # DNAT Rule Collection Group - Inbound Traffic
# resource "azurerm_firewall_policy_rule_collection_group" "dnat_rules" {
#   name               = "DnatRuleCollectionGroup"
#   firewall_policy_id = data.azurerm_firewall_policy.alz_firewall_policy.id
#   priority           = 100

#   # DNAT rules can be added here for inbound traffic if needed
#   # Example: Allow RDP/SSH access from internet to specific VMs
# }

# Note: Azure Firewall configuration is currently commented out
# When needed, uncomment the firewall configuration in settings.connectivity.tf and the rules above
