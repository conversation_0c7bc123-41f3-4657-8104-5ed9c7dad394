# Outputs for Azure AD Groups Module

output "groups" {
  description = "Information about created Azure AD groups"
  value = {
    for key, group in azuread_group.groups : key => {
      object_id    = group.object_id
      display_name = group.display_name
      description  = group.description
      mail         = group.mail
    }
  }
}

output "group_object_ids" {
  description = "Map of group keys to their object IDs"
  value = {
    for key, group in azuread_group.groups : key => group.object_id
  }
}

output "azure_role_assignments" {
  description = "List of Azure RBAC role assignments created"
  value = concat(
    [for assignment in azurerm_role_assignment.subscription_roles : {
      scope     = assignment.scope
      role_name = assignment.role_definition_name
      principal_id = assignment.principal_id
    }],
    [for assignment in azurerm_role_assignment.all_subscriptions_roles : {
      scope     = assignment.scope
      role_name = assignment.role_definition_name
      principal_id = assignment.principal_id
    }]
  )
}

output "subscription_scopes" {
  description = "List of subscription scopes where roles were assigned"
  value = distinct(concat(
    [for assignment in azurerm_role_assignment.subscription_roles : assignment.scope],
    [for assignment in azurerm_role_assignment.all_subscriptions_roles : assignment.scope]
  ))
}
