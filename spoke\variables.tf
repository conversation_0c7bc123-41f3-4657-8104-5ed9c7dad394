# Use variables to customize the spoke deployment

variable "root_id" {
  type        = string
  description = "Sets the value used for generating unique resource naming within the module."
}

variable "location" {
  type        = string
  description = "Sets the default location for spoke resources to be created in. Individual spokes can override this in settings.spoke.tf"
}

variable "spoke_resources_tags" {
  type        = map(string)
  description = "Specify tags to add to spoke resources."
  default = {
    deployedBy = "CTS"
    type       = "spoke-resources"
  }
}

variable "hub_vnet_id" {
  type        = string
  description = "Resource ID of the hub virtual network for peering."
}

variable "hub_vnet_name" {
  type        = string
  description = "Name of the hub virtual network for peering."
}

variable "hub_resource_group_name" {
  type        = string
  description = "Resource group name of the hub virtual network."
}

variable "hub_address_space" {
  type        = string
  description = "Address space of the hub network for NSG rules."
  default     = "**********/16"
}

variable "management_address_space" {
  type        = string
  description = "Address space of the management network for NSG rules."
  default     = "***********/21"
}

variable "use_remote_gateways" {
  type        = bool
  description = "Controls whether to use remote gateways for spoke to hub peering."
  default     = false
}

variable "allow_gateway_transit" {
  type        = bool
  description = "Controls whether to allow gateway transit for hub to spoke peering."
  default     = true
}

variable "route_table_id" {
  type        = string
  description = "Resource ID of the route table to associate with spoke subnets."
  default     = null
}

variable "enable_route_table_association" {
  type        = bool
  description = "Enable route table association with spoke subnets. Set to false during initial deployment."
  default     = false
}
