# Output connectivity configuration using ALZ module

# Additional outputs for spoke modules compatibility
output "hub_network" {
  depends_on = [module.alz_connectivity]
  description = "Hub network information for spoke modules."
  value = {
    # Reference actual ALZ resources to ensure proper dependency
    resource_group_name = "${var.root_id}-connectivity-${var.primary_location}"
    vnet_name          = "${var.root_id}-hub-${var.primary_location}"
    vnet_id            = "/subscriptions/${var.subscription_id_connectivity}/resourceGroups/${var.root_id}-connectivity-${var.primary_location}/providers/Microsoft.Network/virtualNetworks/${var.root_id}-hub-${var.primary_location}"
    address_space      = ["**********/20"]
  }
}

# Route tables for spoke networks and management network
output "route_tables" {
  description = "Route tables for spoke networks and management network."
  value = {
    production_spokes = {
      name = try(azurerm_route_table.production_spoke_route_table.name, "")
      id   = try(azurerm_route_table.production_spoke_route_table.id, "")
    }
    nonproduction_spokes = {
      name = try(azurerm_route_table.nonproduction_spoke_route_table.name, "")
      id   = try(azurerm_route_table.nonproduction_spoke_route_table.id, "")
    }
    management = {
      name = try(azurerm_route_table.management_route_table.name, "")
      id   = try(azurerm_route_table.management_route_table.id, "")
    }
  }
}

# Azure Firewall information
output "azure_firewall" {
  description = "Azure Firewall information."
  value = {
    enabled    = true
    private_ip = "**********"  # Standard Azure Firewall private IP
    subnet     = "**********/26"
  }
}

# Azure Bastion information (managed by ALZ module)
output "azure_bastion" {
  description = "Azure Bastion information."
  value = {
    enabled = true
    subnet  = "***********/26"
    sku     = "Standard"
    note    = "Azure Bastion is managed by ALZ module. Check ALZ module outputs for detailed information."
  }
}
