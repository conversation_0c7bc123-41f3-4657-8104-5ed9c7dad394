# Configure Terraform to set the required AzureRM provider
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# Get all enabled spoke configurations
locals {
  # Merge all spoke configurations
  all_spokes = merge(
    local.spoke_networks_config.production,
    local.spoke_networks_config.non_production
  )

  # Filter only enabled spokes
  enabled_spokes = {
    for spoke_key, spoke_config in local.all_spokes : spoke_key => spoke_config
    if spoke_config.enabled == true
  }
}

# Resource Group for each Spoke Network
resource "azurerm_resource_group" "spoke_network" {
  for_each = local.enabled_spokes

  name     = "${var.root_id}-rg-${each.value.name}-${substr(each.value.location, 0, 3)}"
  location = each.value.location
  tags     = var.spoke_resources_tags
}

# Spoke Virtual Networks
resource "azurerm_virtual_network" "spoke_vnet" {
  for_each = local.enabled_spokes

  name                = "${var.root_id}-vnet-${each.value.name}-${substr(each.value.location, 0, 3)}"
  location            = azurerm_resource_group.spoke_network[each.key].location
  resource_group_name = azurerm_resource_group.spoke_network[each.key].name
  address_space       = each.value.address_space
  tags                = var.spoke_resources_tags
}

# Create a flat map of all subnets across all enabled spokes
locals {
  # Create spoke_subnet combinations
  spoke_subnets = flatten([
    for spoke_key, spoke_config in local.enabled_spokes : [
      for subnet_key, subnet_config in spoke_config.subnets : {
        spoke_key    = spoke_key
        subnet_key   = subnet_key
        spoke_config = spoke_config
        subnet_config = subnet_config
        combined_key = "${spoke_key}-${subnet_key}"
      }
    ]
  ])

  # Convert to map for for_each
  spoke_subnets_map = {
    for item in local.spoke_subnets : item.combined_key => item
  }
}

# Spoke Subnets - Dynamic creation for all enabled spokes
resource "azurerm_subnet" "spoke_subnets" {
  for_each = local.spoke_subnets_map

  name                 = "${var.root_id}-subnet-${each.value.spoke_config.name}-${each.value.subnet_config.name}"
  resource_group_name  = azurerm_resource_group.spoke_network[each.value.spoke_key].name
  virtual_network_name = azurerm_virtual_network.spoke_vnet[each.value.spoke_key].name
  address_prefixes     = each.value.subnet_config.address_prefixes

  # Add delegation if specified
  dynamic "delegation" {
    for_each = try(each.value.subnet_config.delegation, null) != null ? [each.value.subnet_config.delegation] : []
    content {
      name = delegation.value.name
      service_delegation {
        actions = delegation.value.service_delegation.actions
        name    = delegation.value.service_delegation.name
      }
    }
  }
}

# Network Security Groups for each subnet in each spoke
resource "azurerm_network_security_group" "spoke_nsg" {
  for_each = local.spoke_subnets_map

  name                = "${var.root_id}-nsg-${each.value.spoke_config.name}-${each.value.subnet_key}"
  location            = azurerm_resource_group.spoke_network[each.value.spoke_key].location
  resource_group_name = azurerm_resource_group.spoke_network[each.value.spoke_key].name

  # Merge common NSG rules with spoke-specific rules
  dynamic "security_rule" {
    for_each = merge(
      local.common_nsg_rules,
      try(each.value.spoke_config.nsg_rules, {})
    )
    content {
      name                       = security_rule.value.name
      priority                   = security_rule.value.priority
      direction                  = security_rule.value.direction
      access                     = security_rule.value.access
      protocol                   = security_rule.value.protocol
      source_port_range          = security_rule.value.source_port_range
      destination_port_range     = security_rule.value.destination_port_range
      source_address_prefix      = security_rule.value.source_address_prefix
      destination_address_prefix = security_rule.value.destination_address_prefix
    }
  }

  tags = var.spoke_resources_tags
}

# Associate NSG with Spoke Subnets
resource "azurerm_subnet_network_security_group_association" "spoke_nsg_associations" {
  for_each = local.spoke_subnets_map

  subnet_id                 = azurerm_subnet.spoke_subnets[each.key].id
  network_security_group_id = azurerm_network_security_group.spoke_nsg[each.key].id
}

# VNet Peering from each Spoke to Hub
resource "azurerm_virtual_network_peering" "spoke_to_hub" {
  for_each = local.enabled_spokes

  name                      = "${each.value.name}-to-hub"
  resource_group_name       = azurerm_resource_group.spoke_network[each.key].name
  virtual_network_name      = azurerm_virtual_network.spoke_vnet[each.key].name
  remote_virtual_network_id = var.hub_vnet_id

  allow_virtual_network_access = true
  allow_forwarded_traffic      = true
  allow_gateway_transit        = false
  use_remote_gateways          = var.use_remote_gateways
}

# VNet Peering from Hub to each Spoke
resource "azurerm_virtual_network_peering" "hub_to_spoke" {
  for_each = local.enabled_spokes

  name                      = "hub-to-${each.value.name}"
  resource_group_name       = var.hub_resource_group_name
  virtual_network_name      = var.hub_vnet_name
  remote_virtual_network_id = azurerm_virtual_network.spoke_vnet[each.key].id

  allow_virtual_network_access = true
  allow_forwarded_traffic      = true
  allow_gateway_transit        = var.allow_gateway_transit
  use_remote_gateways          = false
}

# Associate Route Table with Spoke Subnets (if provided)
# Note: Route table associations will be created in a separate step after route tables are created
resource "azurerm_subnet_route_table_association" "spoke_route_table_associations" {
  for_each = var.enable_route_table_association ? local.spoke_subnets_map : {}

  subnet_id      = azurerm_subnet.spoke_subnets[each.key].id
  route_table_id = var.route_table_id
}
