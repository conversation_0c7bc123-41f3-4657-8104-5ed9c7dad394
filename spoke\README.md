# Spoke Module - Configuration-Driven Approach

## Tổng quan

Spoke module đã được refactor để sử dụng **configuration-driven approach** thông qua file `settings.spoke.tf`. <PERSON><PERSON><PERSON> g<PERSON><PERSON> bạn chỉ cần:

1. ✅ <PERSON><PERSON><PERSON>nh spoke networks trong `settings.spoke.tf`
2. ✅ Instantiate module với `spoke_name` trong `main.tf`
3. ✅ Tất cả chi tiết (subnets, NSG rules, etc.) được quản lý tự động

## Cấu trúc mới

### **1. Configuration trong `settings.spoke.tf`**

```hcl
spoke_networks_config = {
  production = {
    product_a = {
      enabled       = true
      name          = "product-a"
      address_space = ["**********/23"]
      size          = "small"
      environment   = "production"
      subnets = {
        shared_resource = {
          name             = "shared-resource"
          address_prefixes = ["**********/23"]
          purpose          = "Subnet cho các tài nguyên..."
          usable_ips       = "********** - ************"
          usable_hosts     = 507
        }
      }
      nsg_rules = {
        allow_http = {
          name                       = "Allow_HTTP_Inbound"
          priority                   = 1100
          direction                  = "Inbound"
          access                     = "Allow"
          protocol                   = "Tcp"
          source_port_range          = "*"
          destination_port_range     = "80"
          source_address_prefix      = "*"
          destination_address_prefix = "*"
        }
        # ... more custom rules
      }
      route_table_type = "production"
    }
  }
}
```

### **2. Simplified Module Usage trong `main.tf`**

```hcl
# Trước (Manual configuration)
module "spoke_product_a" {
  source = "./spoke"
  
  spoke_name    = "product-a"
  address_space = ["**********/23"]
  subnets = {
    shared_resource = {
      name             = "shared-resource"
      address_prefixes = ["**********/23"]
      purpose          = "..."
    }
  }
  # ... 20+ lines of configuration
}

# Sau (Configuration-driven)
module "spoke_product_a" {
  source = "./spoke"
  
  root_id      = var.root_id
  spoke_name   = "product-a"  # Must match key in settings.spoke.tf
  location     = var.primary_location
  # Tất cả configuration khác được load từ settings.spoke.tf!
}
```

## Lợi ích

### **1. Centralized Configuration**
- ✅ Tất cả spoke networks được định nghĩa trong 1 file
- ✅ Dễ dàng so sánh và maintain configurations
- ✅ Consistent naming và structure

### **2. Simplified Module Usage**
- ✅ Chỉ cần 3-4 parameters thay vì 20+
- ✅ Không cần duplicate configuration
- ✅ Validation tự động cho spoke_name

### **3. Flexible NSG Rules**
- ✅ Common rules được apply cho tất cả spokes
- ✅ Custom rules cho từng spoke riêng biệt
- ✅ Automatic rule merging

### **4. Rich Metadata**
- ✅ Usable IPs và hosts information
- ✅ Environment và size classification
- ✅ Purpose documentation

## Cách thêm Spoke Network mới

### Bước 1: Thêm configuration vào `settings.spoke.tf`

```hcl
spoke_networks_config = {
  production = {
    # ... existing spokes
    new_product = {
      enabled       = true
      name          = "new-product"
      address_space = ["***********/20"]
      size          = "medium"
      environment   = "production"
      subnets = {
        shared_resource = {
          name             = "shared-resource"
          address_prefixes = ["***********/20"]
          purpose          = "New product resources"
          usable_ips       = "*********** - *************"
          usable_hosts     = 4091
        }
      }
      nsg_rules = {
        allow_custom_port = {
          name                       = "Allow_Custom_Port"
          priority                   = 1102
          direction                  = "Inbound"
          access                     = "Allow"
          protocol                   = "Tcp"
          source_port_range          = "*"
          destination_port_range     = "9000"
          source_address_prefix      = "**********/16"
          destination_address_prefix = "*"
        }
      }
      route_table_type = "production"
    }
  }
}
```

### Bước 2: Update validation trong `variables.tf`

```hcl
validation {
  condition = contains([
    "product-a", "product-microservice", "product-c",
    "uat", "devtest-a", "new-product"  # Add new spoke name
  ], var.spoke_name)
  error_message = "The spoke_name must be one of: ..."
}
```

### Bước 3: Instantiate module trong `main.tf`

```hcl
module "spoke_new_product" {
  source = "./spoke"
  
  root_id      = var.root_id
  spoke_name   = "new-product"  # Must match key in settings.spoke.tf
  location     = var.primary_location
  # ... other standard parameters
}
```

## NSG Rules System

### **Common Rules** (applied to all spokes):
- Allow traffic from Hub network
- Allow traffic from Management network  
- Allow SSH/RDP from Management
- Deny all other inbound traffic

### **Custom Rules** (per spoke):
- HTTP/HTTPS access
- Application-specific ports
- Inter-spoke communication rules

### **Rule Merging**:
```hcl
merged_nsg_rules = merge(
  local.common_nsg_rules,      # Base rules for all spokes
  local.spoke_config.nsg_rules # Spoke-specific rules
)
```

## Troubleshooting

### 1. **Invalid spoke_name error**
```
Error: The spoke_name must be one of: product-a, product-microservice...
```
**Solution**: Đảm bảo `spoke_name` match với key trong `settings.spoke.tf`

### 2. **Configuration not found**
```
Error: local.spoke_config is null
```
**Solution**: Kiểm tra spoke configuration có tồn tại trong `settings.spoke.tf`

### 3. **NSG rule conflicts**
```
Error: Priority 1100 already exists
```
**Solution**: Đảm bảo priorities trong custom rules không conflict với common rules

## Best Practices

1. **Naming Convention**: Sử dụng kebab-case cho spoke names
2. **Priority Ranges**: 
   - Common rules: 1000-1299
   - Custom rules: 1300-3999
   - Deny all: 4096
3. **Address Planning**: Đảm bảo không overlap giữa các spoke networks
4. **Documentation**: Luôn cập nhật `purpose` và `usable_ips` fields
