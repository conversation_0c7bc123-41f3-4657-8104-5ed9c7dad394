# Configure Terraform to set the required AzureRM provider
# version and features{} block

# terraform {
#   required_providers {
#     azurerm = {
#       source  = "hashicorp/azurerm"
#       version = "~> 3.107"
#     }
#   }
# }

# Define the provider configuration

provider "azurerm" {
  features {}

  subscription_id = var.subscription_id_management
}

# Get the current client configuration from the AzureRM provider

data "azurerm_client_config" "current" {}

# Local values for management configuration
locals {
  configure_management_resources = {
    settings = {
      log_analytics = {
        enabled = true
        config = {
          retention_in_days                           = var.log_retention_in_days
          enable_monitoring_for_arc                   = true
          enable_monitoring_for_vm                    = var.enable_monitoring_for_vm
          enable_monitoring_for_vmss                  = var.enable_monitoring_for_vmss
          enable_solution_for_agent_health_assessment = true
          enable_solution_for_anti_malware            = true
          enable_solution_for_change_tracking         = var.enable_change_tracking
          enable_solution_for_service_map             = true
          enable_solution_for_sql_assessment          = true
          enable_solution_for_sql_vulnerability_assessment = true
          enable_solution_for_sql_advanced_threat_detection = true
          enable_solution_for_updates                 = true
          enable_solution_for_vm_insights             = true
          enable_sentinel                             = var.enable_sentinel
        }
      }
      asc_export_resource_group_name = "${var.root_id}-rg-management-${var.primary_location}"
      security_center = {
        enabled = false
        config = {
          email_security_contact                                = var.security_alerts_email_address
          enable_defender_for_apis                              = var.enable_defender_for_apis
          enable_defender_for_app_services                      = var.enable_defender_for_app_services
          enable_defender_for_arm                               = var.enable_defender_for_arm
          enable_defender_for_containers                        = var.enable_defender_for_containers
          enable_defender_for_cosmosdbs                         = var.enable_defender_for_cosmosdbs
          enable_defender_for_cspm                              = var.enable_defender_for_cspm
          enable_defender_for_dns                               = var.enable_defender_for_dns
          enable_defender_for_key_vault                         = var.enable_defender_for_key_vault
          enable_defender_for_oss_databases                     = var.enable_defender_for_oss_databases
          enable_defender_for_servers                           = var.enable_defender_for_servers
          enable_defender_for_servers_vulnerability_assessments = var.enable_defender_for_servers_vulnerability_assessments
          enable_defender_for_sql_servers                       = var.enable_defender_for_sql_servers
          enable_defender_for_sql_server_vms                    = var.enable_defender_for_sql_server_vms
          enable_defender_for_storage                           = var.enable_defender_for_storage
        }
      }
    }
  }
}

# Declare the Azure landing zones Terraform module
# and provide the connectivity configuration.

# Create management resource group with custom naming convention
resource "azurerm_resource_group" "management" {
  name     = "${var.root_id}-rg-management-${var.primary_location}"
  location = var.primary_location
  tags     = var.tags
}

module "alz" {
  source  = "Azure/caf-enterprise-scale/azurerm"
  version = "6.2.0" # change this to your desired version, https://www.terraform.io/language/expressions/version-constraints

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm
    azurerm.management   = azurerm
  }
  default_location = var.primary_location

  # Base module configuration settings
  root_parent_id = data.azurerm_client_config.current.tenant_id
  root_id        = var.root_id

  # Disable creation of the core management group hierarchy
  # as this is being created by the core module instance
  deploy_core_landing_zones = false

  # Configuration settings for management resources
  # Disable ALZ management resources to avoid conflicts with custom implementation
  deploy_management_resources    = false
  configure_management_resources = local.configure_management_resources
  subscription_id_management     = var.subscription_id_management

}

# Log Analytics module
module "log_analytics" {
  source = "./modules/log_analytics"

  resource_prefix       = var.root_id
  resource_group_name   = azurerm_resource_group.management.name
  location              = var.primary_location
  retention_in_days     = var.log_retention_in_days
  enable_sentinel       = var.enable_sentinel
  enable_change_tracking = var.enable_change_tracking
  enable_monitoring_for_vm = var.enable_monitoring_for_vm
  enable_monitoring_for_vmss = var.enable_monitoring_for_vmss
  tags                  = var.tags
}

# Security Center module
module "security_center" {
  source = "./modules/security_center"

  email_security_contact                                = var.security_alerts_email_address
  log_analytics_workspace_id                            = module.log_analytics.workspace_id
  enable_defender_for_apis                              = var.enable_defender_for_apis
  enable_defender_for_app_services                      = var.enable_defender_for_app_services
  enable_defender_for_arm                               = var.enable_defender_for_arm
  enable_defender_for_containers                        = var.enable_defender_for_containers
  enable_defender_for_cosmosdbs                         = var.enable_defender_for_cosmosdbs
  enable_defender_for_cspm                              = var.enable_defender_for_cspm
  enable_defender_for_dns                               = var.enable_defender_for_dns
  enable_defender_for_key_vault                         = var.enable_defender_for_key_vault
  enable_defender_for_oss_databases                     = var.enable_defender_for_oss_databases
  enable_defender_for_servers                           = var.enable_defender_for_servers
  enable_defender_for_servers_vulnerability_assessments = var.enable_defender_for_servers_vulnerability_assessments
  enable_defender_for_sql_servers                       = var.enable_defender_for_sql_servers
  enable_defender_for_sql_server_vms                    = var.enable_defender_for_sql_server_vms
  enable_defender_for_storage                           = var.enable_defender_for_storage
}

# Azure Monitor Agent module
module "azure_monitor_agent" {
  source = "./modules/azure_monitor_agent"

  resource_prefix                      = var.root_id
  resource_group_name                  = azurerm_resource_group.management.name
  location                             = var.primary_location
  log_analytics_workspace_id           = module.log_analytics.workspace_id
  enable_ama                           = var.enable_ama
  enable_vminsights_dcr                = var.enable_vminsights_dcr
  enable_change_tracking_dcr           = var.enable_change_tracking_dcr
  enable_mdfc_defender_for_sql_dcr     = var.enable_mdfc_defender_for_sql_dcr
  tags                                 = var.tags
}
