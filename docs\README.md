# 📚 Hướng Dẫn Sử Dụng EWH Landing Zone Terraform

Chào mừng bạn đến với tài liệu hướng dẫn chi tiết về EWH Azure Landing Zone. Tài liệu này cung cấp hướng dẫn từng bước để triển khai và quản lý Azure Landing Zone sử dụng Terraform.

## 📖 Mục Lục

### 🏠 [Tổng Quan Chung](./README-vi.md)
Giới thiệu tổng quan về kiến trúc, yêu cầu tiên quyết, và hướng dẫn bắt đầu nhanh.

### 🏗️ Hướng Dẫn Từng Module

| Module | Mô Tả | Tài Liệu |
|--------|-------|----------|
| **Core** | Management Groups, Policies, Azure AD Groups | [📋 Core Module](./core-module.md) |
| **Management** | Log Analytics, Security Center, Monitoring | [📊 Management Module](./management-module.md) |
| **Connectivity** | Hub Network, Firewall, VPN Gateway | [🌐 Connectivity Module](./connectivity-module.md) |
| **Spoke** | Application Networks, VNet Peering | [🔗 Spoke Module](./spoke-module.md) |

## 🚀 Quy Trình Triển Khai

### Bước 1: Chuẩn Bị
```bash
# Clone repository
git clone <repository-url>
cd ewh-landingzone

# Cấu hình Azure CLI
az login
az account set --subscription "your-subscription-id"

# Kiểm tra Terraform
terraform --version
```

### Bước 2: Cấu Hình Biến
```bash
# Copy file cấu hình mẫu
cp terraform.tfvars.example terraform.tfvars

# Chỉnh sửa với giá trị thực tế
nano terraform.tfvars
```

### Bước 3: Triển Khai Theo Thứ Tự

#### 3.1 Core Module (Đầu tiên)
```bash
terraform init
terraform plan -target=module.core
terraform apply -target=module.core
```

#### 3.2 Management Module (Thứ hai)
```bash
terraform plan -target=module.management
terraform apply -target=module.management
```

#### 3.3 Connectivity Module (Thứ ba)
```bash
terraform plan -target=module.connectivity
terraform apply -target=module.connectivity
```

#### 3.4 Spoke Networks (Cuối cùng)
```bash
# Bật spoke deployment trong terraform.tfvars
deploy_spokes = true

# Cấu hình spoke networks trong spoke/settings.spoke.tf
# Đặt enabled = true cho các spoke muốn triển khai

# Triển khai spoke networks
terraform plan -target=module.spokes
terraform apply -target=module.spokes

# Hoặc triển khai tất cả cùng lúc
terraform plan
terraform apply
```

## 📋 Checklist Triển Khai

### ✅ Trước Khi Bắt Đầu
- [ ] Azure CLI đã được cài đặt và cấu hình
- [ ] Terraform >= 1.0 đã được cài đặt
- [ ] Có quyền Owner/Contributor trên subscriptions
- [ ] Đã chuẩn bị subscription IDs
- [ ] Đã xác định email security contact

### ✅ Cấu Hình terraform.tfvars
- [ ] `root_id` - Tiền tố tổ chức
- [ ] `root_name` - Tên hiển thị tổ chức
- [ ] `primary_location` - Vùng Azure chính
- [ ] `subscription_id_management` - Management subscription
- [ ] `subscription_id_connectivity` - Connectivity subscription
- [ ] `email_security_contact` - Email bảo mật
- [ ] `tags` - Tags bắt buộc theo policy

### ✅ Sau Triển Khai Core Module
- [ ] Management Groups được tạo đúng cấu trúc
- [ ] Azure Policies được assign
- [ ] Azure AD Groups được tạo (nếu có)
- [ ] Subscriptions được gán đúng Management Groups

### ✅ Sau Triển Khai Management Module
- [ ] Log Analytics Workspace hoạt động
- [ ] Microsoft Defender for Cloud được cấu hình
- [ ] Security alerts được thiết lập
- [ ] Data Collection Rules hoạt động

### ✅ Sau Triển Khai Connectivity Module
- [ ] Hub Virtual Network được tạo
- [ ] Azure Firewall hoạt động
- [ ] VPN Gateway được cấu hình (nếu có)
- [ ] Azure Bastion hoạt động
- [ ] DDoS Protection được bật (nếu có)

### ✅ Cấu Hình Spoke Deployment
- [ ] `deploy_spokes` - Bật/tắt spoke deployment
- [ ] `spoke_resources_tags` - Tags cho spoke resources
- [ ] Cấu hình `enabled = true` trong `spoke/settings.spoke.tf` cho các spoke cần triển khai

### ✅ Sau Triển Khai Spoke Networks
- [ ] Tất cả enabled spoke networks được tạo
- [ ] VNet Peering hoạt động cho từng spoke
- [ ] Network Security Groups được cấu hình
- [ ] Route Tables được associate (nếu có)
- [ ] Connectivity tests pass cho tất cả spokes

## 🔧 Biến Cấu Hình Quan Trọng

### Biến Bắt Buộc Chung
```hcl
root_id                      = "ewh"
root_name                    = "EWH"
primary_location             = "southeastasia"
secondary_location           = "eastasia"
subscription_id_management   = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_connectivity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
email_security_contact       = "<EMAIL>"
```

### Biến Tùy Chọn Quan Trọng
```hcl
enable_ddos_protection = false  # Bật cho production
log_retention_in_days  = 60     # Tùy theo compliance
assign_roles_to_all_subscriptions = false  # Cẩn thận với true

# Spoke deployment control
deploy_spokes = false  # Bật để deploy tất cả enabled spoke networks
```

## 💰 Ước Tính Chi Phí

### Chi Phí Cơ Bản (USD/tháng)
| Component | Chi Phí | Ghi Chú |
|-----------|---------|---------|
| Log Analytics | $50-200 | Tùy theo data volume |
| Azure Firewall | $1,256 | Standard SKU |
| VPN Gateway | $142 | VpnGw1 SKU |
| Defender for Cloud | $15/server | Tùy theo số servers |
| Azure Bastion | $87 | Standard SKU |

### Chi Phí Tùy Chọn
| Component | Chi Phí | Khi Nào Bật |
|-----------|---------|-------------|
| DDoS Protection | $2,944 | Nhiều public IPs |
| Microsoft Sentinel | $500-2000 | SIEM requirements |
| Firewall Premium | $1,884 | Advanced security |

## 🆘 Hỗ Trợ và Troubleshooting

### Lỗi Phổ Biến

#### 1. Insufficient Permissions
```
Error: insufficient privileges to complete the operation
```
**Giải pháp**: Kiểm tra quyền Owner/Contributor trên subscription

#### 2. Subscription Not Found
```
Error: subscription "xxx" was not found
```
**Giải pháp**: Kiểm tra subscription ID trong terraform.tfvars

#### 3. Resource Already Exists
```
Error: A resource with the ID "xxx" already exists
```
**Giải pháp**: Import resource hoặc xóa resource trùng lặp

### Debug Commands
```bash
# Bật debug logging
export TF_LOG=DEBUG

# Kiểm tra state
terraform state list
terraform state show <resource>

# Refresh state
terraform refresh

# Import resource
terraform import <resource_type>.<name> <resource_id>
```

### Liên Hệ Hỗ Trợ
- **Platform Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Emergency**: Tạo ticket trong hệ thống IT Service Management

## 📚 Tài Liệu Tham Khảo

### Microsoft Documentation
- [Azure Landing Zones](https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/ready/landing-zone/)
- [Enterprise Scale Architecture](https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/ready/enterprise-scale/)
- [Azure Firewall Documentation](https://docs.microsoft.com/en-us/azure/firewall/)

### Terraform Documentation
- [Azure Provider](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs)
- [Terraform Best Practices](https://www.terraform.io/docs/cloud/guides/recommended-practices/index.html)

### Internal Resources
- EWH Cloud Governance Policy
- EWH Security Standards
- EWH Naming Conventions

## 📄 Changelog

### Version 1.0.0 (Current)
- Initial release với 4 modules chính
- Hỗ trợ single/multi-subscription deployment
- Tích hợp Microsoft Defender for Cloud
- Hỗ trợ Azure AD Groups management

### Planned Features
- Multi-region deployment automation
- Advanced monitoring dashboards
- Cost optimization recommendations
- Automated compliance reporting

---

**Lưu ý**: Tài liệu này được cập nhật thường xuyên. Vui lòng kiểm tra phiên bản mới nhất trước khi triển khai.
