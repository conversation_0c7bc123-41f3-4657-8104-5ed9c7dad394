{"name": "require_mandatory_tags_with_rules", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "properties": {"displayName": "Require mandatory tags on resources with specific rules", "description": "Enforce mandatory tags with defined formats or allowed values for EWH Landing Zone resources.", "policyType": "Custom", "mode": "Indexed", "metadata": {"version": "1.0.0", "category": "Tags"}, "parameters": {"Owner": {"type": "String", "metadata": {"displayName": "Owner tag name", "description": "Name of the Owner tag"}, "defaultValue": "Owner"}, "org": {"type": "String", "metadata": {"displayName": "Organization tag name", "description": "Name of the Organization tag"}, "defaultValue": "Organization"}, "created_by": {"type": "String", "metadata": {"displayName": "Created By tag name", "description": "Name of the Created By tag"}, "defaultValue": "CreatedBy"}, "operation_team": {"type": "String", "metadata": {"displayName": "Operation Team tag name", "description": "Name of the Operation Team tag"}, "defaultValue": "OperationTeam"}, "project_name": {"type": "String", "metadata": {"displayName": "Project Name tag name", "description": "Name of the Project Name tag"}, "defaultValue": "ProjectName"}, "env": {"type": "String", "metadata": {"displayName": "Environment tag name", "description": "Name of the Environment tag"}, "defaultValue": "Environment"}, "app_name": {"type": "String", "metadata": {"displayName": "Application Name tag name", "description": "Name of the Application Name tag"}, "defaultValue": "ApplicationName"}, "resource_type": {"type": "String", "metadata": {"displayName": "Resource Type tag name", "description": "Name of the Resource Type tag"}, "defaultValue": "ResourceType"}, "priority": {"type": "String", "metadata": {"displayName": "Priority tag name", "description": "Name of the Priority tag"}, "defaultValue": "Priority"}, "data_zone": {"type": "String", "metadata": {"displayName": "Data Zone tag name", "description": "Name of the Data Zone tag"}, "defaultValue": "DataZone"}, "cost_center": {"type": "String", "metadata": {"displayName": "Cost Center tag name", "description": "Name of the Cost Center tag"}, "defaultValue": "CostCenter"}, "managed_by": {"type": "String", "metadata": {"displayName": "Managed By tag name", "description": "Name of the Managed By tag"}, "defaultValue": "ManagedBy"}}, "policyRule": {"if": {"anyOf": [{"not": {"field": "[concat('tags[', parameters('Owner'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('org'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('created_by'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('operation_team'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('project_name'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('env'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('app_name'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('resource_type'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('priority'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('data_zone'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('cost_center'), ']')]", "exists": true}}, {"not": {"field": "[concat('tags[', parameters('managed_by'), ']')]", "exists": true}}]}, "then": {"effect": "deny"}}}}