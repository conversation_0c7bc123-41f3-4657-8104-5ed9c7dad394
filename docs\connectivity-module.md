# Connectivity Module - Hub Networking

Connectivity Module chịu trách nhiệm triển khai hạ tầng mạng hub, bao gồm Virtual Network, Azure Firewall, VPN Gateway, và các thành phần connectivity khác.

## 🎯 Mục Đ<PERSON>ch

- <PERSON><PERSON><PERSON> khai Hub Virtual Network theo mô hình Hub-Spoke
- Cấu hình Azure Firewall cho traffic filtering
- <PERSON><PERSON><PERSON><PERSON> lập VPN Gateway cho kết nối hybrid
- Cấu hình DDoS Protection (tùy chọn)
- Thiết lập Bastion Host cho secure access

## 📋 Biến Cấu Hình

### Biến Bắt Buộc

```hcl
# Cấu hình cơ bản
root_id = "ewh"
# Mô tả: Tiền tố tổ chức, phải khớp với Core Module

primary_location = "southeastasia"
# Mô tả: Vùng Azure chính để triển khai hub network

secondary_location = "eastasia"
# Mô tả: Vùng Azure phụ (cho disaster recovery)

subscription_id_connectivity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
# Mô tả: Subscription ID cho connectivity resources
# Có thể giống với subscription_id_management

enable_ddos_protection = false
# Mô tả: Bật Azure DDoS Protection Standard
# Chi phí: ~$2,944/tháng + $30/protected IP
# Khuyến nghị: true cho production với nhiều public IPs

connectivity_resources_tags = {
  Purpose   = "Networking"
  Component = "Connectivity"
  Network   = "Hub-Spoke"
  Traffic   = "Internal"
}
# Mô tả: Tags riêng cho connectivity resources
```

## 🏗️ Tài Nguyên Được Tạo

### Hub Virtual Network

```hcl
# Cấu hình mặc định trong settings.connectivity.tf
hub_virtual_networks = {
  primary = {
    name                            = "vnet-hub-sea"
    resource_group_name            = "rg-connectivity-sea"
    location                       = "southeastasia"
    address_space                  = ["**********/16"]
    
    subnets = {
      # Azure Firewall Subnet (bắt buộc tên AzureFirewallSubnet)
      AzureFirewallSubnet = {
        address_prefixes = ["**********/24"]
      }
      
      # Gateway Subnet cho VPN/ExpressRoute
      GatewaySubnet = {
        address_prefixes = ["**********/24"]
      }
      
      # Azure Bastion Subnet
      AzureBastionSubnet = {
        address_prefixes = ["**********/24"]
      }
      
      # Management Subnet
      management = {
        address_prefixes = ["***********/21"]
      }
    }
  }
  
  # Secondary hub (tùy chọn)
  secondary = {
    name                = "vnet-hub-ea"
    resource_group_name = "rg-connectivity-ea"
    location           = "eastasia"
    address_space      = ["**********/16"]
    # ... tương tự primary
  }
}
```

### Azure Firewall

- **SKU**: Standard hoặc Premium
- **Availability Zones**: Được bật nếu region hỗ trợ
- **Public IP**: Dedicated public IP
- **Firewall Policy**: Centralized rule management

### VPN Gateway

- **SKU**: VpnGw1 (có thể nâng cấp)
- **Type**: Route-based
- **Active-Active**: Tùy chọn
- **BGP**: Được bật

### Azure Bastion

- **SKU**: Standard
- **Subnet**: AzureBastionSubnet (/26 minimum)
- **Public IP**: Dedicated public IP

### DDoS Protection Plan (Tùy Chọn)

- **Type**: Standard
- **Protected Resources**: Tất cả public IPs trong hub

## 🔧 Cách Cấu Hình

### 1. Cấu Hình Cơ Bản (Single Region)

```hcl
# terraform.tfvars
root_id                      = "ewh"
primary_location             = "southeastasia"
secondary_location           = "eastasia"
subscription_id_connectivity = "your-connectivity-subscription-id"
enable_ddos_protection       = false

connectivity_resources_tags = {
  Purpose   = "Hub Network"
  Component = "Connectivity"
  Tier      = "Infrastructure"
}
```

### 2. Cấu Hình Production (Multi-Region với DDoS)

```hcl
# Bật DDoS Protection cho production
enable_ddos_protection = true

# Tags chi tiết hơn
connectivity_resources_tags = {
  Purpose     = "Hub Network"
  Component   = "Connectivity"
  Tier        = "Infrastructure"
  Environment = "Production"
  Criticality = "High"
  DR          = "Yes"
}
```

### 3. Tùy Chỉnh Network Configuration

Chỉnh sửa file `connectivity/settings.connectivity.tf`:

```hcl
# Tùy chỉnh address spaces
locals {
  configure_connectivity_resources = {
    settings = {
      hub_networks = [
        {
          enabled = true
          config = {
            address_space                = ["**********/16"]  # Thay đổi theo nhu cầu
            location                     = var.primary_location
            link_to_ddos_protection_plan = var.enable_ddos_protection
            
            subnets = [
              {
                name             = "AzureFirewallSubnet"
                address_prefixes = ["**********/24"]
              },
              {
                name             = "GatewaySubnet"
                address_prefixes = ["**********/24"]
              },
              {
                name             = "AzureBastionSubnet"
                address_prefixes = ["**********/24"]
              }
            ]
          }
        }
      ]
    }
  }
}
```

### 4. Cấu Hình Azure Firewall Rules

Chỉnh sửa file `connectivity/firewall-rules.tf`:

```hcl
# Application Rules
resource "azurerm_firewall_application_rule_collection" "example" {
  name               = "app-rules"
  azure_firewall_name = azurerm_firewall.hub_firewall.name
  resource_group_name = azurerm_resource_group.connectivity.name
  priority           = 100
  action             = "Allow"

  rule {
    name = "allow-microsoft"
    source_addresses = ["*"]
    target_fqdns = [
      "*.microsoft.com",
      "*.windows.net",
      "*.azure.com"
    ]
    protocol {
      port = "443"
      type = "Https"
    }
  }
}

# Network Rules
resource "azurerm_firewall_network_rule_collection" "example" {
  name               = "network-rules"
  azure_firewall_name = azurerm_firewall.hub_firewall.name
  resource_group_name = azurerm_resource_group.connectivity.name
  priority           = 100
  action             = "Allow"

  rule {
    name                  = "allow-dns"
    source_addresses      = ["10.0.0.0/8"]
    destination_ports     = ["53"]
    destination_addresses = ["*******", "*******"]
    protocols             = ["TCP", "UDP"]
  }
}
```

## 🌐 Network Design

### Address Space Planning

| Component | Primary (SEA) | Secondary (EA) | Purpose |
|-----------|---------------|----------------|---------|
| Hub VNet | **********/16 | **********/16 | Hub networks |
| Firewall | **********/24 | **********/24 | Azure Firewall |
| Gateway | **********/24 | **********/24 | VPN/ExpressRoute |
| Bastion | **********/24 | **********/24 | Bastion Host |
| Management | ***********/21 | ***********/21 | Management VMs |
| Spokes | **********/14 | **********/14 | Application workloads |

### Traffic Flow

```
Internet ←→ Azure Firewall ←→ Hub VNet ←→ Spoke VNets
                ↓
            On-premises (via VPN Gateway)
```

## 💰 Ước Tính Chi Phí

### Azure Firewall

| SKU | Chi Phí (USD/tháng) | Throughput | Features |
|-----|---------------------|------------|----------|
| Standard | ~$1,256 | 30 Gbps | Basic filtering |
| Premium | ~$1,884 | 100 Gbps | IDPS, TLS inspection |

### VPN Gateway

| SKU | Chi Phí (USD/tháng) | Throughput | Tunnels |
|-----|---------------------|------------|---------|
| VpnGw1 | ~$142 | 650 Mbps | 30 |
| VpnGw2 | ~$365 | 1 Gbps | 30 |
| VpnGw3 | ~$1,168 | 1.25 Gbps | 30 |

### Azure Bastion

- **Standard**: ~$87/tháng
- **Premium**: ~$174/tháng (với additional features)

### DDoS Protection

- **Standard**: ~$2,944/tháng + $30/protected public IP

## 🚀 Triển Khai

```bash
# Triển khai Connectivity Module (sau Management Module)
terraform plan -target=module.connectivity
terraform apply -target=module.connectivity

# Kiểm tra outputs
terraform output connectivity_outputs
```

## 🔍 Kiểm Tra Sau Triển Khai

### 1. Hub Virtual Network

```bash
# Kiểm tra VNet
az network vnet list --resource-group rg-connectivity-sea --output table

# Kiểm tra subnets
az network vnet subnet list --vnet-name vnet-hub-sea --resource-group rg-connectivity-sea --output table
```

### 2. Azure Firewall

```bash
# Kiểm tra Firewall status
az network firewall show --name fw-hub-sea --resource-group rg-connectivity-sea --query "provisioningState"

# Kiểm tra public IP
az network public-ip show --name pip-fw-hub-sea --resource-group rg-connectivity-sea --query "ipAddress"
```

### 3. Connectivity Tests

- Test internet connectivity qua Firewall
- Test VPN connectivity (nếu có)
- Test Bastion access
- Kiểm tra DNS resolution

## ⚠️ Lưu Ý Quan Trọng

### Security

- **Firewall Rules**: Chỉ mở ports cần thiết
- **NSGs**: Áp dụng defense-in-depth
- **Bastion**: Sử dụng thay vì public IPs cho VMs

### Performance

- **Firewall SKU**: Chọn theo throughput requirements
- **Availability Zones**: Bật cho high availability
- **Gateway SKU**: Chọn theo bandwidth needs

### Cost Optimization

- **DDoS Protection**: Chỉ bật nếu có nhiều public IPs
- **Firewall**: Cân nhắc Standard vs Premium
- **Bastion**: Có thể tắt khi không sử dụng

### Monitoring

- **Firewall Logs**: Bật diagnostic logs
- **Network Watcher**: Sử dụng cho troubleshooting
- **Alerts**: Thiết lập cho high bandwidth usage
