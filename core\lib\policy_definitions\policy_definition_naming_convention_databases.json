{"name": "naming_convention_databases", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Databases", "description": "This policy enforces naming conventions for database resources (SQL Server, MySQL) according to EWH Landing Zone organizational standards.", "policyType": "Custom", "mode": "All", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'sql'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/servers"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'mysql'))]", "equals": true}]}]}]}, "then": {"effect": "deny"}}}}