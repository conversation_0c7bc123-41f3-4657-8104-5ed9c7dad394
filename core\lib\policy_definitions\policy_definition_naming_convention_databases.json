{"name": "naming_convention_databases", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Databases", "description": "This policy enforces naming conventions for database resources (SQL Server, MySQL) according to EWH Landing Zone organizational standards.", "policyType": "Custom", "mode": "All", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {"orgList": {"type": "Array", "defaultValue": ["ewh", "abc", "xyz"], "metadata": {"displayName": "Organization Codes", "description": "Allowed organization codes."}}, "envList": {"type": "Array", "defaultValue": ["prod", "dev", "test", "stag"], "metadata": {"displayName": "Environment Names", "description": "Allowed environment values."}}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'sql'))]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/servers"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'mysql'))]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}]}]}]}, "then": {"effect": "deny"}}}}