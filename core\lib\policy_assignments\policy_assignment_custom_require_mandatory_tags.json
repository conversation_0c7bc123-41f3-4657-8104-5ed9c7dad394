{"name": "Require-Mandatory-Tags", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy requires mandatory tags on resources to ensure proper governance and cost management for EWH Landing Zone.", "displayName": "Require mandatory tags on resources", "notScopes": [], "parameters": {"Owner": {"value": "Owner"}, "org": {"value": "Organization"}, "created_by": {"value": "CreatedBy"}, "operation_team": {"value": "OperationTeam"}, "project_name": {"value": "ProjectName"}, "env": {"value": "Environment"}, "app_name": {"value": "ApplicationName"}, "resource_type": {"value": "ResourceType"}, "priority": {"value": "Priority"}, "data_zone": {"value": "Public"}, "cost_center": {"value": "CostCenter"}, "managed_by": {"value": "ManagedBy"}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/require_mandatory_tags_with_rules", "nonComplianceMessages": [{"message": "Resources must have all mandatory tags assigned: Owner, Organization, CreatedBy, OperationTeam, ProjectName, Environment, ApplicationName, ResourceType, Priority, DataZone, CostCenter, and ManagedBy."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}