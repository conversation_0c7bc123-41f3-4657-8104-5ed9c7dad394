# Azure Monitor Agent Module
# Provides Azure Monitor Agent and Data Collection Rules

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# User Assigned Managed Identity for AMA
resource "azurerm_user_assigned_identity" "ama" {
  count               = var.enable_ama ? 1 : 0
  name                = "${var.resource_prefix}-uami-ama-${var.location}"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags                = var.tags
}

# VM Insights Data Collection Rule
resource "azurerm_monitor_data_collection_rule" "vm_insights" {
  count               = var.enable_vminsights_dcr ? 1 : 0
  name                = "${var.resource_prefix}-dcr-vminsights-${var.location}"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags                = var.tags

  destinations {
    log_analytics {
      workspace_resource_id = var.log_analytics_workspace_id
      name                  = "VMInsightsPerf-Logs-Dest"
    }
  }

  data_flow {
    streams      = ["Microsoft-InsightsMetrics"]
    destinations = ["VMInsightsPerf-Logs-Dest"]
  }

  data_flow {
    streams      = ["Microsoft-ServiceMap"]
    destinations = ["VMInsightsPerf-Logs-Dest"]
  }

  data_sources {
    performance_counter {
      streams                       = ["Microsoft-InsightsMetrics"]
      sampling_frequency_in_seconds = 60
      counter_specifiers = [
        "\\VmInsights\\DetailedMetrics"
      ]
      name = "VMInsightsPerfCounters"
    }

    extension {
      streams        = ["Microsoft-ServiceMap"]
      extension_name = "DependencyAgent"
      name           = "DependencyAgentDataSource"
    }
  }

  description = "Data collection rule for VM Insights"
}

# Change Tracking Data Collection Rule
resource "azurerm_monitor_data_collection_rule" "change_tracking" {
  count               = var.enable_change_tracking_dcr ? 1 : 0
  name                = "${var.resource_prefix}-dcr-changetracking-${var.location}"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags                = var.tags

  destinations {
    log_analytics {
      workspace_resource_id = var.log_analytics_workspace_id
      name                  = "ChangeTracking-Logs-Dest"
    }
  }

  data_flow {
    streams      = ["Microsoft-ConfigurationChange", "Microsoft-ConfigurationChangeV2"]
    destinations = ["ChangeTracking-Logs-Dest"]
  }

  data_sources {
    extension {
      streams        = ["Microsoft-ConfigurationChange", "Microsoft-ConfigurationChangeV2"]
      extension_name = "ChangeTracking-Windows"
      extension_json = jsonencode({
        enableFiles             = true
        enableSoftware          = true
        enableRegistry          = true
        enableServices          = true
        enableInventory         = true
        fileSettings = {
          enableRecursion = true
        }
        softwareSettings = {
          enableUpdateAgent = true
        }
        inventorySettings = {
          enableRegistry = true
          enableFiles    = true
          enableSoftware = true
          enableServices = true
        }
      })
      name = "ChangeTrackingDataSource"
    }
  }

  description = "Data collection rule for Change Tracking"
}

# Microsoft Defender for SQL Data Collection Rule
resource "azurerm_monitor_data_collection_rule" "defender_sql" {
  count               = var.enable_mdfc_defender_for_sql_dcr ? 1 : 0
  name                = "${var.resource_prefix}-dcr-defendersql-${var.location}"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags                = var.tags

  destinations {
    log_analytics {
      workspace_resource_id = var.log_analytics_workspace_id
      name                  = "DefenderSQL-Logs-Dest"
    }
  }

  data_flow {
    streams      = ["Microsoft-DefenderForSqlAlerts", "Microsoft-DefenderForSqlLogins", "Microsoft-DefenderForSqlTelemetry", "Microsoft-DefenderForSqlScanEvents", "Microsoft-DefenderForSqlScanResults"]
    destinations = ["DefenderSQL-Logs-Dest"]
  }

  data_sources {
    extension {
      streams        = ["Microsoft-DefenderForSqlAlerts", "Microsoft-DefenderForSqlLogins", "Microsoft-DefenderForSqlTelemetry", "Microsoft-DefenderForSqlScanEvents", "Microsoft-DefenderForSqlScanResults"]
      extension_name = "MicrosoftDefenderForSQL"
      extension_json = jsonencode({
        enableCollectionOfSqlQueriesForSecurityResearch = false
      })
      name = "DefenderForSQLDataSource"
    }
  }

  description = "Data collection rule for Microsoft Defender for SQL"
}

# Data Collection Rule Association for VM Insights (example)
# Note: This would typically be applied to specific VMs or VM Scale Sets
# resource "azurerm_monitor_data_collection_rule_association" "vm_insights" {
#   count                   = var.enable_vminsights_dcr ? 1 : 0
#   name                    = "vm-insights-dcr-association"
#   target_resource_id      = var.target_vm_resource_id
#   data_collection_rule_id = azurerm_monitor_data_collection_rule.vm_insights[0].id
# }
