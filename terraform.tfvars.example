# C<PERSON><PERSON> hình cơ bản
root_id                      = "sgr"
root_name                    = "SunGroup"
primary_location             = "southeastasia"
secondary_location           = "eastasia"

# Subscription IDs - Thay thế bằng subscription IDs thực tế của bạn
subscription_id_connectivity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_identity     = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_management   = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

# Security contact
email_security_contact       = "<EMAIL>"

# Log retention
log_retention_in_days        = 60

# DDoS Protection
enable_ddos_protection = true  # Enable DDoS protection for hub virtual networks

# ==============================================================================
# AZURE AD GROUPS CONFIGURATION
# ==============================================================================

# Example Azure AD Groups (uncomment and modify as needed)
# azure_ad_groups = {
#   "ewh-landing-zone-admins" = {
#     display_name       = "EWH Landing Zone Administrators"
#     description        = "Administrators for the EWH Landing Zone"
#     security_enabled   = true
#     assignable_to_role = true
#     mail_enabled       = false
#     members            = ["<EMAIL>", "<EMAIL>"]
#     additional_owners  = ["<EMAIL>"]
#     azure_roles        = ["Owner", "Contributor"]
#     directory_roles    = []
#   }
#   "ewh-security-team" = {
#     display_name       = "EWH Security Team"
#     description        = "Security team members"
#     security_enabled   = true
#     assignable_to_role = true
#     mail_enabled       = false
#     members            = ["<EMAIL>", "<EMAIL>"]
#     additional_owners  = ["<EMAIL>"]
#     azure_roles        = ["Security Reader", "Security Admin"]
#     directory_roles    = []
#   }
# }

# Role Assignment Configuration
assign_roles_to_all_subscriptions = false  # Set to true to assign roles across all subscriptions
enable_directory_role_assignments = false   # Set to true if you have Privileged Role Administrator permissions

# ==============================================================================
# TAGGING CONFIGURATION
# ==============================================================================

# Default tags for all resources - MUST include all mandatory tags
tags = {
  # Mandatory Tags (Required by Policy)
  Owner           = "<EMAIL>"
  Organization    = "EWH"
  CreatedBy       = "<EMAIL>"
  OperationTeam   = "Platform Team"
  ProjectName     = "EWH Landing Zone"
  Environment     = "Production"
  ApplicationName = "Platform Infrastructure"
  ResourceType    = "Infrastructure"
  Priority        = "High"
  DataZone        = "Internal"
  CostCenter      = "IT-001"
  ManagedBy       = "Terraform"
  
  # Additional Tags (Optional but Recommended)
  BusinessUnit    = "IT"
  Compliance      = "ISO27001"
  BackupRequired  = "Yes"
  Monitoring      = "Yes"
  DRPlan          = "Yes"
}

# Additional tags for connectivity resources
connectivity_resources_tags = {
  Purpose   = "Networking"
  Component = "Connectivity"
  Network   = "Hub-Spoke"
  Traffic   = "Internal"
}

# Additional tags for management resources
management_resources_tags = {
  deployedBy = "CTS"
  type       = "management-resources"
  environment = "production"
}

# ==============================================================================
# SPOKE DEPLOYMENT CONFIGURATION
# ==============================================================================

# Control spoke deployment - set to true to deploy all enabled spoke networks
# Individual spokes are controlled by 'enabled' flag in spoke/settings.spoke.tf
deploy_spokes = false

# Tags for spoke resources
spoke_resources_tags = {
  deployedBy = "CTS"
  type       = "spoke-resources"
  workload   = "application"
  tier       = "production"
}
