# Azure Monitor Agent Module Variables

variable "resource_prefix" {
  type        = string
  description = "Prefix for resource naming"
}

variable "resource_group_name" {
  type        = string
  description = "Name of the resource group"
}

variable "location" {
  type        = string
  description = "Azure region for resources"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resources"
  default     = {}
}

variable "log_analytics_workspace_id" {
  type        = string
  description = "Log Analytics workspace ID for data collection rules"
}

# AMA Configuration
variable "enable_ama" {
  type        = bool
  description = "Enable Azure Monitor Agent (AMA) configuration"
  default     = true
}

variable "enable_vminsights_dcr" {
  type        = bool
  description = "Enable VM Insights Data Collection Rule"
  default     = true
}

variable "enable_change_tracking_dcr" {
  type        = bool
  description = "Enable Change Tracking Data Collection Rule"
  default     = true
}

variable "enable_mdfc_defender_for_sql_dcr" {
  type        = bool
  description = "Enable Microsoft Defender for Cloud SQL Data Collection Rule"
  default     = false
}

# Optional: Target resources for DCR associations
variable "target_vm_resource_ids" {
  type        = list(string)
  description = "List of VM resource IDs to associate with data collection rules"
  default     = []
}

variable "target_vmss_resource_ids" {
  type        = list(string)
  description = "List of VMSS resource IDs to associate with data collection rules"
  default     = []
}
