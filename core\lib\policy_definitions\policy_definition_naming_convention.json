{"name": "naming_convention", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Enforce EWH Landing Zone Naming Convention", "description": "This policy enforces naming conventions for Azure resources according to EWH Landing Zone standards.", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {"orgPrefix": {"type": "String", "defaultValue": "ewh", "metadata": {"displayName": "Organization Prefix", "description": "Required organization prefix for resource names."}}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-vm-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), 'st*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-vnet-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-nsg-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/loadBalancers"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-lb-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/applicationGateways"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-agw-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/azureFirewalls"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-afw-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworkGateways"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-vgw-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.OperationalInsights/workspaces"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-law-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-kv-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Web/sites"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-app-*')]"}}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"not": {"field": "name", "like": "[concat(parameters('orgPrefix'), '-rg-*')]"}}]}]}, "then": {"effect": "deny"}}}}