{"product_a": {"policy_assignments": ["Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Deny-VM-Internet-SSH-RDP", "Enforce-Naming", "Enforce-Naming-Databases", "Enforce-Name-Containers"], "policy_definitions": ["naming_convention", "naming_convention_databases", "naming_convention_containers", "secure_storage_accounts", "enforce_network_security_groups", "require_mandatory_tags_with_rules"], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Priority", "data_zone": "Public", "cost_center": "CostCenter", "managed_by": "ManagedBy"}}, "access_control": {}}}}