{"name": "Secure-Storage-Accounts", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy ensures that secure transfer (HTTPS only) is enabled on storage accounts to protect data in transit.", "displayName": "Require secure transfer on Storage Accounts", "notScopes": [], "parameters": {"effect": {"value": "<PERSON><PERSON>"}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "nonComplianceMessages": [{"message": "Storage accounts {enforcementMode} have secure transfer enabled."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}