# Configure the spoke resources settings.
locals {
  # Spoke Network Configurations
  spoke_networks_config = {
    # Production Networks (**********/16)
    production = {
      product_a = {
        enabled       = true
        name          = "product-a"
        address_space = ["**********/23"]
        location      = var.location
        size          = "small"
        environment   = "production"
        purpose       = "Product A Virtual Network"
        subnets = {
          shared_resource = {
            name             = "shared-resource"
            address_prefixes = ["**********/23"]
            purpose          = "Subnet cho các tài nguyên, có thể chia nhỏ subnet xuống"
            usable_ips       = "********** - ************"
            usable_hosts     = 507
          }
        }
        nsg_rules = {
          allow_http = {
            name                       = "Allow_HTTP_Inbound"
            priority                   = 1100
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "80"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_https = {
            name                       = "Allow_HTTPS_Inbound"
            priority                   = 1101
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "443"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
        }
        route_table_type = "production"
      }
      
      product_microservice = {
        enabled       = true
        name          = "product-microservice"
        address_space = ["**********/22"]
        location      = var.location
        size          = "medium"
        environment   = "production"
        purpose       = "Product Microservice Virtual Network"
        subnets = {
          shared_resource = {
            name             = "shared-resource"
            address_prefixes = ["**********/22"]
            purpose          = "Subnet cho các tài nguyên, có thể chia nhỏ subnet xuống"
            usable_ips       = "********** - ************"
            usable_hosts     = 1019
          }
        }
        nsg_rules = {
          allow_http = {
            name                       = "Allow_HTTP_Inbound"
            priority                   = 1100
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "80"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_https = {
            name                       = "Allow_HTTPS_Inbound"
            priority                   = 1101
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "443"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_microservice_ports = {
            name                       = "Allow_Microservice_Ports"
            priority                   = 1102
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "8000-8999"
            source_address_prefix      = "**********/16"
            destination_address_prefix = "*"
          }
        }
        route_table_type = "production"
      }
      
      product_c = {
        enabled       = true
        name          = "product-c"
        address_space = ["***********/20"]
        location      = var.location
        size          = "large"
        environment   = "production"
        purpose       = "Product C Virtual Network"
        subnets = {
          shared_resource = {
            name             = "shared-resource"
            address_prefixes = ["***********/20"]
            purpose          = "Subnet cho các tài nguyên, có thể chia nhỏ subnet xuống"
            usable_ips       = "*********** - *************"
            usable_hosts     = 4091
          }
        }
        nsg_rules = {
          allow_http = {
            name                       = "Allow_HTTP_Inbound"
            priority                   = 1100
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "80"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_https = {
            name                       = "Allow_HTTPS_Inbound"
            priority                   = 1101
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "443"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
        }
        route_table_type = "production"
      }
    }
    
    # Non-Production Networks (**********/16)
    non_production = {
      uat = {
        enabled       = true
        name          = "uat"
        address_space = ["**********/23"]
        location      = var.location
        size          = "small"
        environment   = "non-production"
        purpose       = "UAT Subscription"
        subnets = {
          shared_resource = {
            name             = "shared-resource"
            address_prefixes = ["**********/23"]
            purpose          = "Subnet cho các tài nguyên, có thể chia nhỏ subnet xuống"
            usable_ips       = "********** - ************"
            usable_hosts     = 507
          }
        }
        nsg_rules = {
          allow_http = {
            name                       = "Allow_HTTP_Inbound"
            priority                   = 1100
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "80"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_https = {
            name                       = "Allow_HTTPS_Inbound"
            priority                   = 1101
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "443"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_dev_ports = {
            name                       = "Allow_Dev_Ports"
            priority                   = 1102
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "3000-3999"
            source_address_prefix      = "**********/16"
            destination_address_prefix = "*"
          }
        }
        route_table_type = "nonproduction"
      }
      
      devtest_a = {
        enabled       = true
        name          = "devtest-a"
        address_space = ["************/20"]
        location      = var.location
        size          = "large"
        environment   = "non-production"
        purpose       = "Devtest A Subscription"
        subnets = {
          shared_resource = {
            name             = "shared-resource"
            address_prefixes = ["************/20"]
            purpose          = "Subnet cho các tài nguyên, có thể chia nhỏ subnet xuống"
            usable_ips       = "************ - **************"
            usable_hosts     = 4091
          }
        }
        nsg_rules = {
          allow_http = {
            name                       = "Allow_HTTP_Inbound"
            priority                   = 1100
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "80"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_https = {
            name                       = "Allow_HTTPS_Inbound"
            priority                   = 1101
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "443"
            source_address_prefix      = "*"
            destination_address_prefix = "*"
          }
          allow_dev_ports = {
            name                       = "Allow_Dev_Ports"
            priority                   = 1102
            direction                  = "Inbound"
            access                     = "Allow"
            protocol                   = "Tcp"
            source_port_range          = "*"
            destination_port_range     = "3000-9999"
            source_address_prefix      = "**********/16"
            destination_address_prefix = "*"
          }
        }
        route_table_type = "nonproduction"
      }
    }
  }

  # Common NSG Rules for all spoke networks
  common_nsg_rules = {
    allow_hub_inbound = {
      name                       = "Allow_Inbound_From_Hub"
      priority                   = 1000
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "*"
      source_port_range          = "*"
      destination_port_range     = "*"
      source_address_prefix      = var.hub_address_space
      destination_address_prefix = "*"
    }
    allow_management_inbound = {
      name                       = "Allow_Inbound_From_Management"
      priority                   = 1001
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "*"
      source_port_range          = "*"
      destination_port_range     = "*"
      source_address_prefix      = var.management_address_space
      destination_address_prefix = "*"
    }
    allow_ssh_from_management = {
      name                       = "Allow_SSH_From_Management"
      priority                   = 1200
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range          = "*"
      destination_port_range     = "22"
      source_address_prefix      = var.management_address_space
      destination_address_prefix = "*"
    }
    allow_rdp_from_management = {
      name                       = "Allow_RDP_From_Management"
      priority                   = 1201
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range          = "*"
      destination_port_range     = "3389"
      source_address_prefix      = var.management_address_space
      destination_address_prefix = "*"
    }
    deny_all_inbound = {
      name                       = "Deny_All_Inbound"
      priority                   = 4096
      direction                  = "Inbound"
      access                     = "Deny"
      protocol                   = "*"
      source_port_range          = "*"
      destination_port_range     = "*"
      source_address_prefix      = "*"
      destination_address_prefix = "*"
    }
  }

  # Set the default location and tags
  location = var.location
  tags     = var.spoke_resources_tags
}
