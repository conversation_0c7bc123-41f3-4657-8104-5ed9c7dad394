# Security Center Module Outputs

output "security_center_contact_id" {
  description = "ID of the Security Center contact"
  value       = var.email_security_contact != null ? azurerm_security_center_contact.main[0].id : null
}

output "defender_plan_ids" {
  description = "IDs of Microsoft Defender plans"
  value = {
    apis                = var.enable_defender_for_apis ? azurerm_security_center_subscription_pricing.apis[0].id : null
    app_services        = var.enable_defender_for_app_services ? azurerm_security_center_subscription_pricing.app_services[0].id : null
    arm                 = var.enable_defender_for_arm ? azurerm_security_center_subscription_pricing.arm[0].id : null
    containers          = var.enable_defender_for_containers ? azurerm_security_center_subscription_pricing.containers[0].id : null
    cosmosdbs           = var.enable_defender_for_cosmosdbs ? azurerm_security_center_subscription_pricing.cosmosdbs[0].id : null
    cspm                = var.enable_defender_for_cspm ? azurerm_security_center_subscription_pricing.cspm[0].id : null
    dns                 = var.enable_defender_for_dns ? azurerm_security_center_subscription_pricing.dns[0].id : null
    key_vault           = var.enable_defender_for_key_vault ? azurerm_security_center_subscription_pricing.key_vault[0].id : null
    oss_databases       = var.enable_defender_for_oss_databases ? azurerm_security_center_subscription_pricing.oss_databases[0].id : null
    servers             = var.enable_defender_for_servers ? azurerm_security_center_subscription_pricing.servers[0].id : null
    sql_servers         = var.enable_defender_for_sql_servers ? azurerm_security_center_subscription_pricing.sql_servers[0].id : null
    sql_server_vms      = var.enable_defender_for_sql_server_vms ? azurerm_security_center_subscription_pricing.sql_server_vms[0].id : null
    storage             = var.enable_defender_for_storage ? azurerm_security_center_subscription_pricing.storage[0].id : null
  }
}

output "auto_provisioning_id" {
  description = "ID of the auto provisioning setting"
  value       = null  # Auto provisioning is deprecated
}

output "workspace_integration_id" {
  description = "ID of the Log Analytics workspace integration"
  value       = null # Temporarily disabled
}

output "security_center_settings" {
  description = "Security Center settings"
  value = {
    mcas  = null  # Settings already exist
    wdatp = null  # Settings already exist
  }
}
